import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import * as Haptics from 'expo-haptics';
import { Plus, Home, User, Heart, Bell } from 'lucide-react-native';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Colors } from '@/lib/constants/Colors';
import { useColorScheme } from '@/lib/hooks/useColorScheme';

export function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const tintColor = Colors[colorScheme ?? 'light'].tint;

  const handleCenterButtonPress = () => {
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    // TODO: Add your center button action here
    console.log('Center button pressed!');
  };

  const handleTabPress = (route: any, isFocused: boolean) => {
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    const event = navigation.emit({
      type: 'tabPress',
      target: route.key,
      canPreventDefault: true,
    });

    if (!isFocused && !event.defaultPrevented) {
      navigation.navigate(route.name, route.params);
    }
  };

  // Filter out hidden tabs and get visible tabs
  const visibleRoutes = state.routes.filter((route, index) => {
    const { options } = descriptors[route.key];
    return options.href !== null;
  });

  // Split tabs into left and right groups (2 tabs each side)
  const leftTabs = visibleRoutes.slice(0, 2);
  const rightTabs = visibleRoutes.slice(2, 4);

  const renderTab = (route: any, index: number) => {
    const { options } = descriptors[route.key];
    const isFocused = state.index === state.routes.findIndex(r => r.key === route.key);

    const tabBarIcon = options.tabBarIcon;

    return (
      <TouchableOpacity
        key={route.key}
        style={styles.tab}
        onPress={() => handleTabPress(route, isFocused)}
        activeOpacity={0.7}
      >
        {tabBarIcon && tabBarIcon({
          focused: isFocused,
          color: isFocused ? tintColor : '#999',
          size: 28
        })}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.wrapper}>
      {/* Tab bar */}
      <View style={[
        styles.container,
        Platform.OS === 'ios' && { paddingBottom: insets.bottom }
      ]}>
        {/* Left tabs */}
        <View style={styles.tabGroup}>
          {leftTabs.map((route, index) => renderTab(route, index))}
        </View>

        {/* Empty space for center button */}
        <View style={styles.centerSpace} />

        {/* Right tabs */}
        <View style={styles.tabGroup}>
          {rightTabs.map((route, index) => renderTab(route, index + 2))}
        </View>
      </View>

      {/* Center button - positioned above the tab bar */}
      <TouchableOpacity
        style={[
          styles.centerButton,
          Platform.OS === 'ios' && { bottom: insets.bottom + 50 },
          Platform.OS === 'android' && { bottom: 50 }
        ]}
        onPress={handleCenterButtonPress}
        activeOpacity={0.8}
      >
        <Plus size={24} color="#fff" strokeWidth={3} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
  },
  container: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 12,
    paddingBottom: Platform.OS === 'android' ? 12 : 0,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  tabGroup: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-around',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    minWidth: 60,
  },
  centerSpace: {
    width: 60,
    height: 1,
  },
  centerButton: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4AC29A',
    alignItems: 'center',
    justifyContent: 'center',
    bottom: 50,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 15,
    zIndex: 999,
    borderWidth: 3,
    borderColor: '#fff',
  },
});
