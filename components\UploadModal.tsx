import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { Camera, ChevronRight, FileText, Search, User, X } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

import { getStoredAssistantData } from '@/api/general/assistantApi';
import { Beneficiary, getBeneficiariesByAssistantId } from '@/api/general/beneficiaryApi';





interface UploadModalProps {
  visible: boolean;
  onClose: () => void;
}

type ModalStep = 'beneficiary-selection' | 'file-upload';

export function UploadModal({ visible, onClose }: UploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<ModalStep>('beneficiary-selection');
  const [selectedBeneficiary, setSelectedBeneficiary] = useState<Beneficiary | null>(null);
  const [beneficiaries, setBeneficiaries] = useState<Beneficiary[]>([]);
  const [loading, setLoading] = useState(false);
  const [filterText, setFilterText] = useState('');
  const { width } = Dimensions.get('window');

  // Filter beneficiaries based on search text
  const filteredBeneficiaries = beneficiaries.filter(beneficiary =>
    beneficiary.name.toLowerCase().includes(filterText.toLowerCase()) ||
    beneficiary.independent.toLowerCase().includes(filterText.toLowerCase())
  );

  // Fetch beneficiaries when modal opens
  useEffect(() => {
    if (visible) {
      fetchBeneficiaries();
    }
  }, [visible]);

  const fetchBeneficiaries = async () => {
    try {
      setLoading(true);
      const assistantData = await getStoredAssistantData();
      if (!assistantData || !assistantData.id) {
        Alert.alert('Error', 'Assistant data not found. Please log in again.');
        return;
      }

      const beneficiariesData = await getBeneficiariesByAssistantId(assistantData.id);
      setBeneficiaries(beneficiariesData);
    } catch (error) {
      console.error('Error fetching beneficiaries:', error);
      Alert.alert('Error', 'Failed to load beneficiaries. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile(result.assets[0]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select file');
    }
  };

  const handleTakePhoto = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile({
          name: `photo_${Date.now()}.jpg`,
          uri: result.assets[0].uri,
          type: 'image/jpeg',
          size: result.assets[0].fileSize || 0,
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const handleSubmit = () => {
    if (selectedFile && selectedBeneficiary) {
      // Handle file upload here
      console.log('Uploading file:', selectedFile);
      console.log('For beneficiary:', selectedBeneficiary);
      Alert.alert('Success', `Document uploaded successfully for ${selectedBeneficiary.name}!`);
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setCurrentStep('beneficiary-selection');
    setSelectedBeneficiary(null);
    setFilterText('');
    onClose();
  };

  const handleBeneficiarySelect = (beneficiary: Beneficiary) => {
    setSelectedBeneficiary(beneficiary);
    setCurrentStep('file-upload');
  };

  const handleBackToBeneficiaries = () => {
    setCurrentStep('beneficiary-selection');
    setSelectedFile(null);
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={handleClose}
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <View style={[
          styles.modalContainer,
          {
            width: width * 0.9,
          }
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>
              {currentStep === 'beneficiary-selection'
                ? 'Select Beneficiary'
                : `Upload document for ${selectedBeneficiary?.name}`
              }
            </Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Content based on current step */}
          {currentStep === 'beneficiary-selection' ? (
            <>
              {/* Description */}
              <Text style={styles.description}>
                Select a beneficiary to attach the document to:
              </Text>

              {/* Search Filter */}
              <View style={styles.searchContainer}>
                <Search size={20} color="#999" />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search beneficiaries..."
                  placeholderTextColor="#999"
                  value={filterText}
                  onChangeText={setFilterText}
                  returnKeyType="search"
                  clearButtonMode="while-editing"
                />
              </View>

              {/* Beneficiaries List */}
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#00D4AA" />
                  <Text style={styles.loadingText}>Loading beneficiaries...</Text>
                </View>
              ) : (
                <ScrollView
                  style={styles.beneficiariesList}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                >
                  {filteredBeneficiaries.length > 0 ? (
                    filteredBeneficiaries.map((beneficiary) => (
                      <TouchableOpacity
                        key={beneficiary.id}
                        style={styles.beneficiaryItem}
                        onPress={() => handleBeneficiarySelect(beneficiary)}
                        activeOpacity={0.7}
                      >
                        <View style={styles.beneficiaryInfo}>
                          <View style={styles.beneficiaryAvatar}>
                            <User size={20} color="#999" />
                          </View>
                          <View style={styles.beneficiaryDetails}>
                            <Text style={styles.beneficiaryName}>{beneficiary.name}</Text>
                            <Text style={styles.beneficiaryStatus}>{beneficiary.independent}</Text>
                          </View>
                        </View>
                        <ChevronRight size={20} color="#999" />
                      </TouchableOpacity>
                    ))
                  ) : (
                    <View style={styles.noResultsContainer}>
                      <Text style={styles.noResultsText}>No beneficiaries found</Text>
                      <Text style={styles.noResultsSubtext}>Try adjusting your search terms</Text>
                    </View>
                  )}
                </ScrollView>
              )}
            </>
          ) : (
            <>
              {/* Back button */}
              <TouchableOpacity style={styles.backButton} onPress={handleBackToBeneficiaries}>
                <Text style={styles.backButtonText}>← Back to Beneficiaries</Text>
              </TouchableOpacity>

              {/* Description */}
              <Text style={styles.description}>
                Upload a document for {selectedBeneficiary?.name}. This could be an ID card, certificate, or any relevant document.
              </Text>

              {/* File Selection Area */}
              <TouchableOpacity
                style={[
                  styles.fileSelectArea,
                  selectedFile && styles.fileSelectAreaSelected
                ]}
                onPress={handleSelectFile}
              >
                <FileText size={40} color={selectedFile ? "#00D4AA" : "#666"} />
                <Text style={[
                  styles.selectFileText,
                  selectedFile && styles.selectFileTextSelected
                ]}>
                  {selectedFile ? selectedFile.name : 'Select File'}
                </Text>
              </TouchableOpacity>

              {/* Or text */}
              <Text style={styles.orText}>or</Text>

              {/* Camera Button */}
              <TouchableOpacity style={styles.cameraButton} onPress={handleTakePhoto}>
                <Camera size={20} color="#fff" />
                <Text style={styles.cameraButtonText}>Open Camera & Take Photo</Text>
              </TouchableOpacity>
            </>
          )}

          {/* Action Buttons */}
          {currentStep === 'file-upload' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.submitButton,
                  selectedFile ? styles.submitButtonActive : styles.submitButtonInactive
                ]}
                onPress={handleSubmit}
                disabled={!selectedFile}
              >
                <Text style={[
                  styles.submitButtonText,
                  selectedFile ? styles.submitButtonTextActive : styles.submitButtonTextInactive
                ]}>
                  Submit
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    flex: 1,
    lineHeight: 30,
  },
  closeButton: {
    padding: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 32,
    lineHeight: 20,
  },
  fileSelectArea: {
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 120,
    backgroundColor: '#f8f9fa',
  },
  fileSelectAreaSelected: {
    borderColor: '#00D4AA',
    backgroundColor: 'rgba(0, 212, 170, 0.1)',
  },
  selectFileText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  selectFileTextSelected: {
    color: '#00D4AA',
  },
  orText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginBottom: 20,
  },
  cameraButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  cameraButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonActive: {
    backgroundColor: '#00D4AA',
  },
  submitButtonInactive: {
    backgroundColor: '#e0e0e0',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  submitButtonTextActive: {
    color: '#fff',
  },
  submitButtonTextInactive: {
    color: '#999',
  },
  // New styles for beneficiary selection
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    color: '#666',
    marginTop: 12,
    fontSize: 16,
  },
  beneficiariesList: {
    maxHeight: 300,
    marginBottom: 20,
  },
  beneficiaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  beneficiaryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  beneficiaryAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  beneficiaryDetails: {
    flex: 1,
  },
  beneficiaryName: {
    color: '#1a1a1a',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  beneficiaryStatus: {
    color: '#666',
    fontSize: 14,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  backButtonText: {
    color: '#00D4AA',
    fontSize: 16,
    fontWeight: '600',
  },
  // Search filter styles
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1a1a1a',
  },
  // No results styles
  noResultsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#999',
  },
});
