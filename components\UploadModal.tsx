import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
  Dimensions,
} from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { Camera, FileText, X } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface UploadModalProps {
  visible: boolean;
  onClose: () => void;
}

export function UploadModal({ visible, onClose }: UploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const insets = useSafeAreaInsets();
  const { width } = Dimensions.get('window');

  const handleSelectFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile(result.assets[0]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select file');
    }
  };

  const handleTakePhoto = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile({
          name: `photo_${Date.now()}.jpg`,
          uri: result.assets[0].uri,
          type: 'image/jpeg',
          size: result.assets[0].fileSize || 0,
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const handleSubmit = () => {
    if (selectedFile) {
      // Handle file upload here
      console.log('Uploading file:', selectedFile);
      Alert.alert('Success', 'File uploaded successfully!');
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={[
          styles.modalContainer,
          { 
            paddingTop: insets.top + 20,
            paddingBottom: insets.bottom + 20,
            width: width * 0.9,
          }
        ]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Upload a photo of your{'\n'}National ID Card 🆔</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          {/* Description */}
          <Text style={styles.description}>
            Regulations require you to upload a national identity card. Don't worry, your data will stay safe and private.
          </Text>

          {/* File Selection Area */}
          <TouchableOpacity 
            style={[
              styles.fileSelectArea,
              selectedFile && styles.fileSelectAreaSelected
            ]}
            onPress={handleSelectFile}
          >
            <FileText size={40} color={selectedFile ? "#00D4AA" : "#666"} />
            <Text style={[
              styles.selectFileText,
              selectedFile && styles.selectFileTextSelected
            ]}>
              {selectedFile ? selectedFile.name : 'Select File'}
            </Text>
          </TouchableOpacity>

          {/* Or text */}
          <Text style={styles.orText}>or</Text>

          {/* Camera Button */}
          <TouchableOpacity style={styles.cameraButton} onPress={handleTakePhoto}>
            <Camera size={20} color="#fff" />
            <Text style={styles.cameraButtonText}>Open Camera & Take Photo</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.submitButton,
                selectedFile ? styles.submitButtonActive : styles.submitButtonInactive
              ]}
              onPress={handleSubmit}
              disabled={!selectedFile}
            >
              <Text style={[
                styles.submitButtonText,
                selectedFile ? styles.submitButtonTextActive : styles.submitButtonTextInactive
              ]}>
                Submit
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 20,
    padding: 24,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    lineHeight: 30,
  },
  closeButton: {
    padding: 4,
  },
  description: {
    fontSize: 14,
    color: '#ccc',
    marginBottom: 32,
    lineHeight: 20,
  },
  fileSelectArea: {
    borderWidth: 2,
    borderColor: '#333',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 120,
  },
  fileSelectAreaSelected: {
    borderColor: '#00D4AA',
    backgroundColor: 'rgba(0, 212, 170, 0.1)',
  },
  selectFileText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  selectFileTextSelected: {
    color: '#00D4AA',
  },
  orText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginBottom: 20,
  },
  cameraButton: {
    backgroundColor: '#333',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  cameraButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#333',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonActive: {
    backgroundColor: '#00D4AA',
  },
  submitButtonInactive: {
    backgroundColor: '#666',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  submitButtonTextActive: {
    color: '#fff',
  },
  submitButtonTextInactive: {
    color: '#999',
  },
});
